# NextChat 认证问题修复指南

## 问题描述

当使用环境变量中的 `BASE_URL` 时，请求没有携带认证信息，导致 API 调用失败。

## 问题原因

1. **客户端配置不正确**：未启用自定义配置或 API Key 未正确设置
2. **认证逻辑问题**：getHeaders 函数中的认证信息获取逻辑有缺陷
3. **配置不一致**：服务端和客户端的配置不匹配

## 修复步骤

### 1. 检查环境变量配置

确保 `.env.local` 文件包含正确的配置：

```bash
# API 服务器地址
BASE_URL=http://newapi-aicrm.qijianshigw.com

# OpenAI API 密钥
OPENAI_API_KEY=sk-Lvy38D0mIMhfCxzUVc9OAaAtdxsVZH5NYp1jGzHW76c1K475

# 访问密码（可选）
CODE=25ac2fe6cacd1fe23c62c6a17d2df08f757cb3b6469d394e1e862cde2233f6ce
```

### 2. 配置客户端设置

1. 打开 NextChat 设置页面
2. 找到"接入设置"部分
3. **启用"使用自定义配置"**
4. 填写以下信息：
   - **OpenAI 接口地址**：`http://newapi-aicrm.qijianshigw.com`
   - **OpenAI API Key**：`sk-Lvy38D0mIMhfCxzUVc9OAaAtdxsVZH5NYp1jGzHW76c1K475`

### 3. 使用认证调试工具

1. 在设置页面找到"认证调试"选项
2. 点击"调试认证"按钮
3. 查看调试信息，确认：
   - ✅ 使用自定义配置：是
   - ✅ API Key 长度：> 0
   - ✅ API Key 有效：是
   - ✅ Authorization 头：已设置

### 4. 运行修复脚本

```bash
# 运行认证修复脚本
node scripts/fix-auth.js
```

这个脚本会：
- 检查环境变量配置
- 生成修复建议
- 创建示例配置文件

## 调试方法

### 1. 浏览器开发者工具

1. 打开开发者工具 (F12)
2. 切换到 Console 标签
3. 查找以下调试信息：
   ```
   [Auth Debug] Provider: OpenAI
   [Auth Debug] Use Custom Config: true
   [Auth Debug] API Key exists: true
   [Auth Debug] Bearer Token: exists
   ```

### 2. 网络请求检查

1. 在开发者工具中切换到 Network 标签
2. 发送一个聊天请求
3. 检查请求头是否包含：
   ```
   Authorization: Bearer sk-Lvy38D0mIMhfCxzUVc9OAaAtdxsVZH5NYp1jGzHW76c1K475
   ```

### 3. 本地存储检查

1. 在开发者工具中切换到 Application 标签
2. 展开 IndexedDB
3. 查找 `access-control` 存储
4. 确认以下字段：
   - `useCustomConfig`: true
   - `openaiApiKey`: 你的 API Key
   - `openaiUrl`: 你的 API 地址

## 常见问题

### Q1: API Key 格式错误
**症状**：认证失败，401 错误
**解决**：确保 API Key 以 `sk-` 开头，格式正确

### Q2: BASE_URL 配置错误
**症状**：连接超时或 404 错误
**解决**：
- 确保 URL 不以斜杠结尾
- 确保 URL 格式正确（http:// 或 https://）

### Q3: 客户端配置未生效
**症状**：仍然使用默认配置
**解决**：
- 确保启用了"使用自定义配置"
- 清除浏览器缓存并重新配置

### Q4: 认证头未设置
**症状**：请求中没有 Authorization 头
**解决**：
- 检查 API Key 是否正确设置
- 使用认证调试工具检查配置

## 技术细节

### 修复内容

1. **改进了 getHeaders 函数**：
   - 更清晰的 API Key 获取逻辑
   - 添加了详细的调试日志
   - 改进了错误处理

2. **增强了 OpenAI API 类**：
   - 添加了配置调试信息
   - 改进了 baseURL 处理逻辑

3. **添加了调试工具**：
   - 认证状态检查组件
   - 修复脚本
   - 详细的调试信息

### 代码变更

主要修改的文件：
- `app/client/api.ts` - 认证逻辑改进
- `app/client/platforms/openai.ts` - OpenAI API 增强
- `app/store/access.ts` - 访问存储调试
- `app/components/auth-debug.tsx` - 调试组件
- `scripts/fix-auth.js` - 修复脚本

## 验证修复

1. 重启应用
2. 打开设置页面，确认配置正确
3. 使用认证调试工具检查状态
4. 发送测试消息，确认正常工作
5. 检查浏览器控制台，确认没有认证错误

如果问题仍然存在，请提供：
- 浏览器控制台的完整日志
- 认证调试工具的输出
- 网络请求的详细信息
