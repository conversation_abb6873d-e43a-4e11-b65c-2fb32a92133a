<div align="center">

<h1 align="center">AINextChat</h1>

基于 [NextChat](https://github.com/ChatGPTNextWeb/NextChat) 定制开发
</div>

## 创建环境变量文件

复制根目录中的`.env.template`，并命名为`.env.local`

## 配置页面访问密码
### `CODE`、`NEXT_PUBLIC_CODE` （必填）
> 配置密码后，所有请求头中的认证信息会被加密而非真是密钥。

> **警告**：请务必将密码的位数设置得足够长，最好 7 位以上，否则[会被爆破](https://github.com/Yidadaa/ChatGPT-Next-Web/issues/518)。

可以使用以下命令生成密钥

``` bash
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

增加或修改该环境变量后，请**重新部署**项目使改动生效。

## 环境变量

> 本项目大多数配置项都通过环境变量来设置，教程：[如何修改 Vercel 环境变量](./docs/vercel-cn.md)。

### `OPENAI_API_KEY` （必填项）

OpenAI 密钥，你在 openai 账户页面申请的 api key，使用英文逗号隔开多个 key，这样可以随机轮询这些 key。

### `BASE_URL` （必填项）

原项目是兼容各家模型 API，本项目已剔除这一配置
> Default: `https://newapi-aicrm.qijianshigw.com`

> 如果遇到 ssl 证书问题，请将 `BASE_URL` 的协议设置为 http。

### `NEXT_PUBLIC_DEFAULT_MODEL` （必填项）
> 前端默认模型：·`ds-r1@QJS`

关系用户首次进入时的模型选择

关键模型配置文件 `app/constant.ts`
``` ts
// 声明模型列表
const qjsModels = [
  "ds-r1",
  "ds-chat",
];

// 插入完整模型列表
export const DEFAULT_MODELS = [
  ...qjsModels.map((name) => ({
    name,
    available: true,
    sorted: seq++,
    provider: {
      id: "qjs",
      providerName: "QJS",
      providerType: "qjs",
      sorted: 1,
    },
  })),
]

```
> 前端默认模型规则，应该为 `<EMAIL>` 否则前端无法被选中

### `DEFAULT_MODEL` （必填项）
> 服务端默认模型：`ds-r1` ，与上述参数类似，只需要模型名称

## 开发

### 本地开发

1. 安装 nodejs 18 和 yarn，具体细节请询问 ChatGPT；
2. 执行 `yarn install && yarn dev` 即可。⚠️ 注意：此命令仅用于本地开发，不要用于部署！
3. 如果你想本地部署，请使用 `yarn install && yarn build && yarn start` 命令，你可以配合 pm2 来守护进程，防止被杀死，详情询问 ChatGPT。

## 构建部署

``` bash
docker-compose up
```

## 开源协议

[MIT](https://opensource.org/license/mit/)

## 数据库示例页面

本项目集成了MySQL数据库操作示例，可以通过访问 `/dbPage` 路由来查看和操作数据库。

### 数据库配置

数据库连接配置在 `app/utils/mysql.ts` 文件中：

```typescript
// MySQL连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: '123456',
  database: 'test',
};
```

### 初始化数据库

提供了初始化数据库的脚本，可以通过以下命令执行：

```bash
# 赋予脚本执行权限
chmod +x ./scripts/init-mysql.sh

# 执行初始化脚本
./scripts/init-mysql.sh
```

脚本会创建 `test` 数据库和 `test_table` 表，并插入一些测试数据。

### 表结构

`test_table` 表结构如下：

```sql
CREATE TABLE IF NOT EXISTS `test_table` (
  `test_name` varchar(100) DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`),
  UNIQUE KEY `test_table_unique` (`id`)
)
```

### API接口

数据库操作API接口路径为 `/api/db`，支持以下操作：

- `GET /api/db`：获取所有记录
- `GET /api/db?id=1`：获取指定ID的记录
- `POST /api/db`：添加新记录，请求体格式为 `{ "name": "测试名称" }`
- `PUT /api/db`：更新记录，请求体格式为 `{ "id": 1, "name": "新名称" }`
- `DELETE /api/db?id=1`：删除指定ID的记录

### 示例页面功能

示例页面位于 `/dbPage` 路由，提供以下功能：

- 显示所有记录列表
- 添加新记录
- 编辑现有记录
- 删除记录
