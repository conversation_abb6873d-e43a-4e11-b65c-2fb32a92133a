import { NextRequest, NextResponse } from "next/server";
import { MySqlUtils } from "@/app/utils/mysql";

// 获取所有记录
export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const id = url.searchParams.get("id");

    let result;
    if (id) {
      // 获取指定ID的记录
      result = await MySqlUtils.getRecordById(Number(id));
    } else {
      // 获取所有记录
      result = await MySqlUtils.getAllRecords();
    }

    return NextResponse.json(result);
  } catch (error: any) {
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 },
    );
  }
}

// 添加记录
export async function POST(req: NextRequest) {
  try {
    const { name } = await req.json();

    if (!name) {
      return NextResponse.json(
        { success: false, error: "缺少必要参数" },
        { status: 400 },
      );
    }

    const result = await MySqlUtils.addRecord(name);
    return NextResponse.json(result);
  } catch (error: any) {
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 },
    );
  }
}

// 更新记录
export async function PUT(req: NextRequest) {
  try {
    const { id, name } = await req.json();

    if (!id || !name) {
      return NextResponse.json(
        { success: false, error: "缺少必要参数" },
        { status: 400 },
      );
    }

    const result = await MySqlUtils.updateRecord(Number(id), name);
    return NextResponse.json(result);
  } catch (error: any) {
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 },
    );
  }
}

// 删除记录
export async function DELETE(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const id = url.searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { success: false, error: "缺少必要参数" },
        { status: 400 },
      );
    }

    const result = await MySqlUtils.deleteRecord(Number(id));
    return NextResponse.json(result);
  } catch (error: any) {
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 },
    );
  }
}
