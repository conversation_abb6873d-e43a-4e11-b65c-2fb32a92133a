import React, { useState, useEffect } from "react";
import { useAccessStore } from "../store";
import { getHeaders } from "../client/api";
import { Modal, showToast } from "./ui-lib";
import Locale from "../locales";

interface AuthDebugInfo {
  useCustomConfig: boolean;
  openaiApiKey: string;
  openaiUrl: string;
  hasValidKey: boolean;
  authHeaders: Record<string, string>;
  serverConfig: any;
}

export function AuthDebugModal(props: { onClose: () => void }) {
  const [debugInfo, setDebugInfo] = useState<AuthDebugInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const accessStore = useAccessStore();

  useEffect(() => {
    const collectDebugInfo = async () => {
      try {
        // 获取当前认证状态
        const headers = getHeaders();
        
        // 获取服务器配置
        let serverConfig = {};
        try {
          const response = await fetch("/api/config", {
            method: "POST",
            headers: getHeaders(),
          });
          serverConfig = await response.json();
        } catch (e) {
          console.error("Failed to fetch server config:", e);
        }

        const info: AuthDebugInfo = {
          useCustomConfig: accessStore.useCustomConfig,
          openaiApiKey: accessStore.openaiApiKey,
          openaiUrl: accessStore.openaiUrl,
          hasValidKey: accessStore.isValidOpenAI(),
          authHeaders: headers,
          serverConfig,
        };

        setDebugInfo(info);
      } catch (error) {
        console.error("Failed to collect debug info:", error);
        showToast("获取调试信息失败");
      } finally {
        setLoading(false);
      }
    };

    collectDebugInfo();
  }, [accessStore]);

  const copyToClipboard = () => {
    if (!debugInfo) return;
    
    const debugText = `NextChat 认证调试信息
================================
使用自定义配置: ${debugInfo.useCustomConfig}
OpenAI URL: ${debugInfo.openaiUrl}
API Key 长度: ${debugInfo.openaiApiKey.length}
API Key 有效: ${debugInfo.hasValidKey}
认证头存在: ${!!debugInfo.authHeaders.Authorization}
服务器配置: ${JSON.stringify(debugInfo.serverConfig, null, 2)}
`;

    navigator.clipboard.writeText(debugText).then(() => {
      showToast("调试信息已复制到剪贴板");
    });
  };

  const testConnection = async () => {
    if (!debugInfo) return;
    
    try {
      setLoading(true);
      
      // 测试连接
      const response = await fetch("/api/openai/models", {
        method: "GET",
        headers: getHeaders(),
      });
      
      if (response.ok) {
        showToast("连接测试成功！");
      } else {
        const errorText = await response.text();
        showToast(`连接测试失败: ${response.status} ${errorText}`);
      }
    } catch (error) {
      showToast(`连接测试失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="modal-mask">
      <Modal
        title="认证调试信息"
        onClose={props.onClose}
        actions={[
          <button key="copy" onClick={copyToClipboard} disabled={!debugInfo}>
            复制调试信息
          </button>,
          <button key="test" onClick={testConnection} disabled={loading}>
            {loading ? "测试中..." : "测试连接"}
          </button>,
          <button key="close" onClick={props.onClose}>
            关闭
          </button>,
        ]}
      >
        <div className="auth-debug-content">
          {loading ? (
            <div>正在收集调试信息...</div>
          ) : debugInfo ? (
            <div>
              <h3>基本配置</h3>
              <ul>
                <li>
                  <strong>使用自定义配置:</strong>{" "}
                  <span className={debugInfo.useCustomConfig ? "success" : "warning"}>
                    {debugInfo.useCustomConfig ? "是" : "否"}
                  </span>
                </li>
                <li>
                  <strong>OpenAI URL:</strong> {debugInfo.openaiUrl || "未设置"}
                </li>
                <li>
                  <strong>API Key 长度:</strong>{" "}
                  <span className={debugInfo.openaiApiKey.length > 0 ? "success" : "error"}>
                    {debugInfo.openaiApiKey.length} 字符
                  </span>
                </li>
                <li>
                  <strong>API Key 有效:</strong>{" "}
                  <span className={debugInfo.hasValidKey ? "success" : "error"}>
                    {debugInfo.hasValidKey ? "是" : "否"}
                  </span>
                </li>
              </ul>

              <h3>认证状态</h3>
              <ul>
                <li>
                  <strong>Authorization 头:</strong>{" "}
                  <span className={debugInfo.authHeaders.Authorization ? "success" : "error"}>
                    {debugInfo.authHeaders.Authorization ? "已设置" : "未设置"}
                  </span>
                </li>
                {debugInfo.authHeaders.Authorization && (
                  <li>
                    <strong>认证类型:</strong>{" "}
                    {debugInfo.authHeaders.Authorization.startsWith("Bearer") ? "Bearer Token" : "其他"}
                  </li>
                )}
              </ul>

              <h3>修复建议</h3>
              <div className="fix-suggestions">
                {!debugInfo.useCustomConfig && (
                  <div className="suggestion warning">
                    ⚠️ 建议启用"使用自定义配置"并设置正确的 API 信息
                  </div>
                )}
                {debugInfo.openaiApiKey.length === 0 && (
                  <div className="suggestion error">
                    ❌ 请设置 OpenAI API Key
                  </div>
                )}
                {!debugInfo.authHeaders.Authorization && (
                  <div className="suggestion error">
                    ❌ 认证头未设置，请检查 API Key 配置
                  </div>
                )}
                {debugInfo.useCustomConfig && debugInfo.hasValidKey && debugInfo.authHeaders.Authorization && (
                  <div className="suggestion success">
                    ✅ 配置看起来正确，如果仍有问题请检查服务器端配置
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div>获取调试信息失败</div>
          )}
        </div>
      </Modal>

      <style jsx>{`
        .auth-debug-content {
          max-height: 500px;
          overflow-y: auto;
          padding: 10px;
        }
        
        .auth-debug-content h3 {
          margin: 20px 0 10px 0;
          color: var(--primary);
        }
        
        .auth-debug-content ul {
          list-style: none;
          padding: 0;
        }
        
        .auth-debug-content li {
          margin: 8px 0;
          padding: 5px;
          background: var(--gray);
          border-radius: 4px;
        }
        
        .success {
          color: #28a745;
          font-weight: bold;
        }
        
        .warning {
          color: #ffc107;
          font-weight: bold;
        }
        
        .error {
          color: #dc3545;
          font-weight: bold;
        }
        
        .fix-suggestions {
          margin-top: 15px;
        }
        
        .suggestion {
          padding: 10px;
          margin: 5px 0;
          border-radius: 4px;
          border-left: 4px solid;
        }
        
        .suggestion.success {
          background: #d4edda;
          border-color: #28a745;
          color: #155724;
        }
        
        .suggestion.warning {
          background: #fff3cd;
          border-color: #ffc107;
          color: #856404;
        }
        
        .suggestion.error {
          background: #f8d7da;
          border-color: #dc3545;
          color: #721c24;
        }
      `}</style>
    </div>
  );
}
