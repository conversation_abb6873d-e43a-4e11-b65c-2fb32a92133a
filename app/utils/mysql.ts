import mysql from "mysql2/promise";

// MySQL连接配置
const dbConfig = {
  host: process.env.MYSQL_HOST || "localhost",
  port: parseInt(process.env.MYSQL_PORT || "3306"),
  user: process.env.MYSQL_USER || "root",
  password: process.env.MYSQL_PASSWORD || "123456",
  database: process.env.MYSQL_DATABASE || "test",
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

// MySQL工具类
export class MySqlUtils {
  /**
   * 执行SQL查询
   * @param sql SQL语句
   * @param params 参数
   * @returns 查询结果
   */
  static async query(sql: string, params: any[] = []) {
    try {
      const [rows] = await pool.execute(sql, params);
      return { success: true, data: rows };
    } catch (error: any) {
      console.error("MySQL查询错误:", error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取所有记录
   * @returns 查询结果
   */
  static async getAllRecords() {
    return this.query("SELECT * FROM test_table");
  }

  /**
   * 通过ID获取记录
   * @param id 记录ID
   * @returns 查询结果
   */
  static async getRecordById(id: number) {
    return this.query("SELECT * FROM test_table WHERE id = ?", [id]);
  }

  /**
   * 添加记录
   * @param name 测试名称
   * @returns 添加结果
   */
  static async addRecord(name: string) {
    return this.query("INSERT INTO test_table (test_name) VALUES (?)", [name]);
  }

  /**
   * 更新记录
   * @param id 记录ID
   * @param name 测试名称
   * @returns 更新结果
   */
  static async updateRecord(id: number, name: string) {
    return this.query("UPDATE test_table SET test_name = ? WHERE id = ?", [
      name,
      id,
    ]);
  }

  /**
   * 删除记录
   * @param id 记录ID
   * @returns 删除结果
   */
  static async deleteRecord(id: number) {
    return this.query("DELETE FROM test_table WHERE id = ?", [id]);
  }
}

export default MySqlUtils;
