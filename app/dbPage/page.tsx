"use client";

import { useState, useEffect } from "react";
import styles from "./styles.module.scss";

interface Record {
  id: number;
  test_name: string;
}

export default function DbPage() {
  const [records, setRecords] = useState<Record[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [newName, setNewName] = useState("");
  const [editingRecord, setEditingRecord] = useState<Record | null>(null);

  // 获取所有记录
  const fetchRecords = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/db");
      const result = await response.json();

      if (result.success) {
        setRecords(result.data as Record[]);
      } else {
        setError(result.error || "获取数据失败");
      }
    } catch (err: any) {
      setError(err.message || "获取数据失败");
    } finally {
      setLoading(false);
    }
  };

  // 添加记录
  const addRecord = async () => {
    if (!newName.trim()) {
      setError("请输入名称");
      return;
    }

    try {
      const response = await fetch("/api/db", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name: newName }),
      });

      const result = await response.json();

      if (result.success) {
        setNewName("");
        fetchRecords();
      } else {
        setError(result.error || "添加数据失败");
      }
    } catch (err: any) {
      setError(err.message || "添加数据失败");
    }
  };

  // 更新记录
  const updateRecord = async () => {
    if (!editingRecord || !editingRecord.test_name.trim()) {
      setError("请输入名称");
      return;
    }

    try {
      const response = await fetch("/api/db", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          id: editingRecord.id,
          name: editingRecord.test_name,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setEditingRecord(null);
        fetchRecords();
      } else {
        setError(result.error || "更新数据失败");
      }
    } catch (err: any) {
      setError(err.message || "更新数据失败");
    }
  };

  // 删除记录
  const deleteRecord = async (id: number) => {
    try {
      const response = await fetch(`/api/db?id=${id}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (result.success) {
        fetchRecords();
      } else {
        setError(result.error || "删除数据失败");
      }
    } catch (err: any) {
      setError(err.message || "删除数据失败");
    }
  };

  // 页面加载时获取记录
  useEffect(() => {
    fetchRecords();
  }, []);

  return (
    <div className={styles.container}>
      <h1>数据库操作示例</h1>

      {error && <div className={styles.error}>{error}</div>}

      <div className={styles.formContainer}>
        <h2>添加记录</h2>
        <div className={styles.formGroup}>
          <input
            type="text"
            value={newName}
            onChange={(e) => setNewName(e.target.value)}
            placeholder="请输入名称"
          />
          <button onClick={addRecord}>添加</button>
        </div>
      </div>

      {editingRecord && (
        <div className={styles.formContainer}>
          <h2>编辑记录</h2>
          <div className={styles.formGroup}>
            <input
              type="text"
              value={editingRecord.test_name}
              onChange={(e) =>
                setEditingRecord({
                  ...editingRecord,
                  test_name: e.target.value,
                })
              }
              placeholder="请输入名称"
            />
            <button onClick={updateRecord}>保存</button>
            <button onClick={() => setEditingRecord(null)}>取消</button>
          </div>
        </div>
      )}

      <div className={styles.tableContainer}>
        <h2>记录列表</h2>
        {loading ? (
          <div>加载中...</div>
        ) : records.length === 0 ? (
          <div>暂无数据</div>
        ) : (
          <table className={styles.table}>
            <thead>
              <tr>
                <th>ID</th>
                <th>名称</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {records.map((record) => (
                <tr key={record.id}>
                  <td>{record.id}</td>
                  <td>{record.test_name}</td>
                  <td>
                    <button onClick={() => setEditingRecord(record)}>
                      编辑
                    </button>
                    <button onClick={() => deleteRecord(record.id)}>
                      删除
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
}
