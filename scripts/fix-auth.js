#!/usr/bin/env node

/**
 * 认证修复脚本
 * 用于诊断和修复认证相关问题
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 NextChat 认证修复工具');
console.log('================================');

// 检查环境变量文件
function checkEnvFile() {
  console.log('\n📋 检查环境变量配置...');
  
  const envFiles = ['.env.local', '.env', '.env.template'];
  let envFound = false;
  
  for (const envFile of envFiles) {
    if (fs.existsSync(envFile)) {
      console.log(`✅ 找到环境变量文件: ${envFile}`);
      
      const content = fs.readFileSync(envFile, 'utf8');
      
      // 检查关键配置
      const hasBaseUrl = content.includes('BASE_URL=');
      const hasApiKey = content.includes('OPENAI_API_KEY=');
      const hasCode = content.includes('CODE=');
      
      console.log(`   - BASE_URL: ${hasBaseUrl ? '✅' : '❌'}`);
      console.log(`   - OPENAI_API_KEY: ${hasApiKey ? '✅' : '❌'}`);
      console.log(`   - CODE: ${hasCode ? '✅' : '❌'}`);
      
      if (hasBaseUrl && hasApiKey) {
        // 提取配置值
        const baseUrlMatch = content.match(/BASE_URL=(.+)/);
        const apiKeyMatch = content.match(/OPENAI_API_KEY=(.+)/);
        
        if (baseUrlMatch && apiKeyMatch) {
          const baseUrl = baseUrlMatch[1].trim();
          const apiKey = apiKeyMatch[1].trim();
          
          console.log(`   - BASE_URL 值: ${baseUrl}`);
          console.log(`   - API Key 长度: ${apiKey.length} 字符`);
          
          if (baseUrl && apiKey.length > 10) {
            console.log('✅ 环境变量配置看起来正确');
            envFound = true;
          }
        }
      }
      
      break;
    }
  }
  
  if (!envFound) {
    console.log('❌ 未找到有效的环境变量配置');
    console.log('\n💡 建议操作:');
    console.log('1. 复制 .env.template 为 .env.local');
    console.log('2. 设置正确的 BASE_URL 和 OPENAI_API_KEY');
  }
  
  return envFound;
}

// 检查客户端配置
function checkClientConfig() {
  console.log('\n🖥️  检查客户端配置...');
  
  // 检查是否有本地存储的配置
  console.log('💡 请在浏览器中检查以下内容:');
  console.log('1. 打开开发者工具 (F12)');
  console.log('2. 进入 Application/Storage -> IndexedDB');
  console.log('3. 查找 access-control 存储');
  console.log('4. 确认 useCustomConfig 和 openaiApiKey 字段');
}

// 生成修复建议
function generateFixSuggestions() {
  console.log('\n🔧 修复建议:');
  console.log('================================');
  
  console.log('\n1. 环境变量配置:');
  console.log('   - 确保 .env.local 文件存在');
  console.log('   - 设置 BASE_URL=你的API服务器地址');
  console.log('   - 设置 OPENAI_API_KEY=你的API密钥');
  console.log('   - 设置 CODE=访问密码(可选)');
  
  console.log('\n2. 客户端配置:');
  console.log('   - 进入设置页面');
  console.log('   - 启用"使用自定义配置"');
  console.log('   - 填写 OpenAI URL 和 API Key');
  
  console.log('\n3. 调试步骤:');
  console.log('   - 打开浏览器开发者工具');
  console.log('   - 查看 Console 中的 [Auth Debug] 日志');
  console.log('   - 检查网络请求的 Authorization 头');
  
  console.log('\n4. 常见问题:');
  console.log('   - API Key 格式错误 (应以 sk- 开头)');
  console.log('   - BASE_URL 末尾不应有斜杠');
  console.log('   - 客户端和服务端配置不一致');
}

// 创建示例配置文件
function createExampleConfig() {
  console.log('\n📝 创建示例配置文件...');
  
  const exampleEnv = `# NextChat 环境变量配置示例
# 复制此文件为 .env.local 并修改相应值

# 客户端默认模型
NEXT_PUBLIC_DEFAULT_MODEL=gpt-3.5-turbo

# 访问密码 (可选)
CODE=your-access-code-here

# API 服务器地址 (必需)
BASE_URL=https://api.openai.com

# OpenAI API 密钥 (必需)
OPENAI_API_KEY=sk-your-api-key-here

# 其他可选配置
# HIDE_USER_API_KEY=false
# DISABLE_GPT4=false
`;

  fs.writeFileSync('.env.example', exampleEnv);
  console.log('✅ 已创建 .env.example 文件');
}

// 主函数
function main() {
  const envOk = checkEnvFile();
  checkClientConfig();
  generateFixSuggestions();
  createExampleConfig();
  
  console.log('\n🎉 修复工具运行完成!');
  console.log('如果问题仍然存在，请检查浏览器控制台中的调试信息。');
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  checkEnvFile,
  checkClientConfig,
  generateFixSuggestions,
  createExampleConfig
};
