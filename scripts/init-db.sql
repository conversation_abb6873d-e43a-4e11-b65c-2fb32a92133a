-- 创建测试数据库
CREATE DATABASE IF NOT EXISTS test;

-- 使用测试数据库
USE test;

-- 创建测试表
CREATE TABLE IF NOT EXISTS `test_table` (
  `test_name` varchar(100) DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`),
  UNIQUE KEY `test_table_unique` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 插入测试数据
INSERT INTO `test_table` (`test_name`) VALUES ('测试数据1');
INSERT INTO `test_table` (`test_name`) VALUES ('测试数据2');
INSERT INTO `test_table` (`test_name`) VALUES ('测试数据3'); 