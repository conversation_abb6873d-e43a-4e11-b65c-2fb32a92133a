version: '3.8'

services:
  chatgpt-next-web:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NODE_ENV=development
    container_name: chatgpt-next-web
    image: nextchat-qjs:latest
    restart: always
    ports:
      - 18202:3000
    env_file:
      - .env.local
    environment:
      - CODE=${CODE}
      - NEXT_PUBLIC_CODE=${CODE}
      - NODE_ENV=development
    networks:
      - nextchat-network

networks:
  nextchat-network:
    driver: bridge